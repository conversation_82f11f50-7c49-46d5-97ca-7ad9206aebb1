import React from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import {
  LayoutDashboard,
  Lightbulb,
  LogOut,
  User,
  Home,
  Download,
  Zap,
  Timer
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { ideaAPI, downloadFile } from '../../utils/api'
import toast from 'react-hot-toast'

const AdminLayout = ({ children, title }) => {
  const location = useLocation()
  const navigate = useNavigate()
  const { user, logout } = useAuth()

  const navigation = [
    {
      name: 'Dashboard',
      href: '/admin/dashboard',
      icon: LayoutDashboard,
      current: location.pathname === '/admin/dashboard'
    },
    {
      name: 'Ideas',
      href: '/admin/ideas',
      icon: Lightbulb,
      current: location.pathname.startsWith('/admin/ideas')
    }
  ]

  const handleLogout = async () => {
    await logout()
    navigate('/admin/login')
  }

  const handleExport = async () => {
    try {
      const response = await ideaAPI.exportCSV()
      const filename = `ideasprint-ideas-${new Date().toISOString().split('T')[0]}.csv`
      downloadFile(response.data, filename)
      toast.success('Ideas exported successfully!')
    } catch (error) {
      toast.error('Failed to export ideas')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              {/* Logo */}
              <div className="flex-shrink-0 flex items-center">
                <Link to="/admin/dashboard" className="flex items-center">
                  <div className="relative mr-3">
                    <div className="bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 p-2 rounded-xl">
                      <Zap className="h-5 w-5 text-white" />
                    </div>
                    <div className="absolute -top-1 -right-1 bg-gradient-to-r from-orange-400 to-red-400 p-1 rounded-full">
                      <Timer className="h-2 w-2 text-white" />
                    </div>
                  </div>
                  <span className="text-xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                    IdeaSprint Admin
                  </span>
                </Link>
              </div>

              {/* Navigation Links */}
              <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                {navigation.map((item) => {
                  const Icon = item.icon
                  return (
                    <Link
                      key={item.name}
                      to={item.href}
                      className={`${
                        item.current
                          ? 'border-primary-500 text-gray-900'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      } inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium`}
                    >
                      <Icon className="h-4 w-4 mr-2" />
                      {item.name}
                    </Link>
                  )
                })}
              </div>
            </div>

            {/* Right side */}
            <div className="flex items-center space-x-4">
              {/* Export Button */}
              <button
                onClick={handleExport}
                className="btn-outline text-sm"
              >
                <Download className="h-4 w-4 mr-2" />
                Export CSV
              </button>

              {/* Public Site Link */}
              <Link
                to="/"
                className="text-gray-500 hover:text-gray-700"
                title="View Public Site"
              >
                <Home className="h-5 w-5" />
              </Link>

              {/* User Menu */}
              <div className="flex items-center space-x-3">
                <div className="flex items-center text-sm text-gray-700">
                  <User className="h-4 w-4 mr-2" />
                  <span>{user?.username}</span>
                </div>
                <button
                  onClick={handleLogout}
                  className="text-gray-500 hover:text-gray-700"
                  title="Logout"
                >
                  <LogOut className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Page Header */}
      {title && (
        <div className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {children}
      </main>
    </div>
  )
}

export default AdminLayout
