import React from 'react'
import { <PERSON> } from 'react-router-dom'
import {
  Lightbulb,
  Trophy,
  Users,
  Rocket,
  CheckCircle,
  ArrowRight,
  Laptop,
  Star,
  Zap,
  Timer,
  Calendar,
  Clock
} from 'lucide-react'

const LandingPage = () => {
  const features = [
    {
      icon: <Lightbulb className="h-8 w-8 text-primary-600" />,
      title: "Share Your Innovation",
      description: "Submit your groundbreaking business, web, or mobile app ideas",
      image: "https://images.unsplash.com/photo-1553484771-371a605b060b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: <Users className="h-8 w-8 text-primary-600" />,
      title: "Join the Community",
      description: "Opportunity to be a part of Community for best 10 ideas with other innovators and entrepreneurs",
      image: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      color: "from-purple-500 to-pink-500"
    },
    {
      icon: <Trophy className="h-8 w-8 text-primary-600" />,
      title: "Win Amazing Prizes",
      description: "The best idea wins a brand new MacBook Air!",
      image: "https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      color: "from-orange-500 to-red-500"
    }
  ]

  const quotes = [
    {
      text: "Innovation distinguishes between a leader and a follower.",
      author: "Steve Jobs",
      role: "Co-founder of Apple"
    },
    {
      text: "The way to get started is to quit talking and begin doing.",
      author: "Walt Disney",
      role: "Founder of Disney"
    },
    {
      text: "Ideas are easy. Implementation is hard.",
      author: "Guy Kawasaki",
      role: "Marketing Specialist"
    }
  ]

  const process = [
    {
      step: "1",
      title: "Submit Your Idea",
      description: "Fill out our comprehensive form with your innovative concept"
    },
    {
      step: "2",
      title: "Expert Review",
      description: "Our team carefully evaluates all submissions"
    },
    {
      step: "3",
      title: "Win the Prize",
      description: "The most innovative idea wins a MacBook Air!"
    }
  ]

  const categories = [
    {
      name: "Business Ideas",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      color: "from-emerald-500 to-teal-500",
      description: "Revolutionary business models and strategies"
    },
    {
      name: "Web Applications",
      image: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      color: "from-blue-500 to-indigo-500",
      description: "Cutting-edge web platforms and solutions"
    },
    {
      name: "Mobile Applications",
      image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      color: "from-purple-500 to-pink-500",
      description: "Innovative mobile apps and experiences"
    },
    {
      name: "Other Innovations",
      image: "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      color: "from-orange-500 to-red-500",
      description: "Breakthrough ideas across all industries"
    }
  ]

  // Sample ideas for the database (these would come from your backend in production)
  const sampleIdeas = [
    {
      id: 1,
      title: "AI-Powered Personal Finance Assistant",
      category: "Mobile Application",
      submitter: "Sarah Chen",
      description: "A mobile app that uses artificial intelligence to help users manage their personal finances, track expenses, and provide personalized investment advice.",
      analysis: "This idea addresses the growing need for financial literacy among young professionals. Our analysis shows strong market potential with the fintech sector growing at 25% annually. The AI component provides personalized insights that traditional budgeting apps lack.",
      marketPotential: "High",
      feasibility: "Medium",
      innovation: "High",
      image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      color: "from-blue-500 to-cyan-500",
      tags: ["AI", "Finance", "Mobile", "Personalization"]
    },
    {
      id: 2,
      title: "Sustainable Food Delivery Network",
      category: "Business",
      submitter: "Marcus Rodriguez",
      description: "A food delivery service that focuses exclusively on sustainable, locally-sourced restaurants and uses electric vehicles for delivery.",
      analysis: "With increasing environmental consciousness, this idea taps into the $150B food delivery market with a sustainability angle. Our research indicates 73% of consumers are willing to pay more for eco-friendly delivery options.",
      marketPotential: "High",
      feasibility: "High",
      innovation: "Medium",
      image: "https://images.unsplash.com/photo-1542838132-92c53300491e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      color: "from-green-500 to-emerald-500",
      tags: ["Sustainability", "Food", "Delivery", "Environment"]
    },
    {
      id: 3,
      title: "Virtual Reality Learning Platform",
      category: "Web Application",
      submitter: "Dr. Emily Watson",
      description: "An immersive VR platform that allows students to experience historical events, explore molecular structures, and practice complex procedures in a safe virtual environment.",
      analysis: "The EdTech VR market is projected to reach $13B by 2026. This platform could revolutionize education by making abstract concepts tangible. Early pilots show 40% improvement in retention rates compared to traditional methods.",
      marketPotential: "Very High",
      feasibility: "Medium",
      innovation: "Very High",
      image: "https://images.unsplash.com/photo-1535223289827-42f1e9919769?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      color: "from-purple-500 to-pink-500",
      tags: ["VR", "Education", "Learning", "Technology"]
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="relative">
                <div className="bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 p-2 rounded-xl mr-3">
                  <Zap className="h-6 w-6 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 bg-gradient-to-r from-orange-400 to-red-400 p-1 rounded-full">
                  <Timer className="h-3 w-3 text-white" />
                </div>
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                IdeaSprint
              </h1>
            </div>
            <nav className="hidden md:flex space-x-8">
              <a href="#features" className="text-gray-500 hover:text-gray-900">Features</a>
              <a href="#categories" className="text-gray-500 hover:text-gray-900">Categories</a>
              <a href="#database" className="text-gray-500 hover:text-gray-900">Idea Database</a>
              <a href="#about" className="text-gray-500 hover:text-gray-900">About</a>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 via-purple-50 via-pink-50 to-orange-50 min-h-screen flex items-center overflow-hidden">
        {/* Background decorations */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-10 left-10 w-72 h-72 bg-gradient-to-r from-blue-300 to-cyan-300 rounded-full mix-blend-multiply filter blur-xl opacity-40 animate-pulse"></div>
        <div className="absolute top-10 right-10 w-72 h-72 bg-gradient-to-r from-purple-300 to-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-40 animate-pulse delay-1000"></div>
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-gradient-to-r from-orange-300 to-red-300 rounded-full mix-blend-multiply filter blur-xl opacity-40 animate-pulse delay-500"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-green-200 to-teal-200 rounded-full mix-blend-multiply filter blur-2xl opacity-20 animate-pulse delay-2000"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">


            <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-8 leading-tight">
              Transform Your
              <span className="text-gradient block"> Ideas Into Reality</span>
            </h1>

            <p className="text-xl md:text-lg text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed">
              Join our community of innovators and entrepreneurs. Share your groundbreaking
              business and technology concepts that could change the world.
            </p>

            {/* Enhanced Prize Banner */}
            <div className="relative bg-gradient-to-br from-white via-blue-50 to-purple-50 backdrop-blur-sm rounded-3xl shadow-2xl p-10 mb-12 max-w-2xl mx-auto border-2 border-gradient-to-r from-blue-200 to-purple-200 hover:shadow-3xl transition-all duration-300 transform hover:-translate-y-2">
              {/* Decorative elements */}
              <div className="absolute -top-2 -right-2 w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full opacity-20 animate-pulse"></div>
              <div className="absolute -bottom-2 -left-2 w-12 h-12 bg-gradient-to-r from-green-400 to-teal-400 rounded-full opacity-20 animate-pulse delay-1000"></div>

              <div className="text-center">
                <div className="bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 p-6 rounded-2xl mb-6 mx-auto w-fit animate-float">
                  <Laptop className="h-16 w-16 text-white" />
                </div>
                <h3 className="text-3xl font-bold text-gray-900 mb-4">🏆 Grand Prize</h3>
                <p className="text-3xl md:text-4xl font-black bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-6 animate-gradient">
                  MacBook Air
                </p>
                <div className="bg-gradient-to-r from-yellow-100 to-orange-100 border-2 border-gradient-to-r from-yellow-300 to-orange-300 rounded-2xl p-4">
                  <div className="flex items-center justify-center">
                    <Star className="h-6 w-6 text-yellow-500 mr-3 animate-pulse" />
                    <span className="text-lg font-bold text-gray-800">Awarded to the most innovative idea</span>
                    <Star className="h-6 w-6 text-yellow-500 ml-3 animate-pulse" />
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                to="/submit"
                className="btn-primary text-xl px-10 py-5 inline-flex items-center shadow-2xl hover:shadow-3xl transform hover:-translate-y-1 transition-all duration-300"
              >
                Submit Your Idea Now
                <ArrowRight className="ml-3 h-6 w-6" />
              </Link>

              <a
                href="#features"
                className="text-gray-600 hover:text-gray-900 font-medium inline-flex items-center transition-colors duration-200"
              >
                Learn More
                <ArrowRight className="ml-2 h-4 w-4" />
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Competition Timeline Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-100 text-blue-800 mb-6">
              <Calendar className="h-4 w-4 mr-2" />
              First Competition Timeline
            </span>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Competition Schedule
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-8">
              Submit unlimited ideas during our competition period. No restrictions on the number of submissions!
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Competition Start */}
            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border-l-4 border-green-500">
              <div className="text-center">
                <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Calendar className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Competition Starts</h3>
                <p className="text-3xl font-bold text-green-600 mb-2">June 1st</p>
                <p className="text-gray-600 text-sm">2025</p>
                <p className="text-gray-500 text-sm mt-3">Begin submitting your innovative ideas</p>
              </div>
            </div>

            {/* Submission Period */}
            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border-l-4 border-blue-500">
              <div className="text-center">
                <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Lightbulb className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Submission Period</h3>
                <p className="text-2xl font-bold text-blue-600 mb-2">15 Days</p>
                <p className="text-gray-600 text-sm">June 1-15, 2025</p>
                <p className="text-gray-500 text-sm mt-3">Unlimited submissions allowed</p>
              </div>
            </div>

            {/* Evaluation Period */}
            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border-l-4 border-orange-500">
              <div className="text-center">
                <div className="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Clock className="h-8 w-8 text-orange-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Evaluation</h3>
                <p className="text-2xl font-bold text-orange-600 mb-2">July 31st</p>
                <p className="text-gray-600 text-sm">2025</p>
                <p className="text-gray-500 text-sm mt-3">Ideas reviewed & evaluated</p>
              </div>
            </div>

            {/* Winner Announcement */}
            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border-l-4 border-purple-500">
              <div className="text-center">
                <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Trophy className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Winner Contact</h3>
                <p className="text-2xl font-bold text-purple-600 mb-2">August 1st</p>
                <p className="text-gray-600 text-sm">2025</p>
                <p className="text-gray-500 text-sm mt-3">Winner contacted via email</p>
              </div>
            </div>
          </div>

          {/* Additional Info */}
          <div className="mt-16 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-3xl p-8 text-white text-center">
            <h3 className="text-2xl font-bold mb-4">🚀 No Limits on Creativity!</h3>
            <p className="text-lg text-purple-100 mb-6 leading-relaxed">
              Submit as many innovative ideas as you want during the competition period.
              Each submission increases your chances of winning the MacBook Air!
            </p>
            <div className="bg-white/20 rounded-2xl p-6 backdrop-blur-sm">
              <p className="text-sm text-purple-100">
                <strong>Prize Delivery:</strong> Winner will be contacted via email on August 1st, 2025
                to share phone number and address for MacBook Air shipment.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-24 bg-gradient-to-b from-white to-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-primary-100 text-primary-800 mb-6">
              <Star className="h-4 w-4 mr-2" />
              Why Choose Innovation Spark
            </span>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Your Ideas Deserve Recognition
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Join thousands of innovators who trust us to showcase their groundbreaking concepts
              and connect with opportunities that matter.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="group relative">
                <div className="bg-white rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                  {/* Feature Image */}
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={feature.image}
                      alt={feature.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                    <div className={`absolute inset-0 bg-gradient-to-r ${feature.color} opacity-20 group-hover:opacity-30 transition-opacity duration-300`}></div>
                  </div>

                  <div className="p-8">
                    <div className="flex justify-center mb-6">
                      <div className={`bg-gradient-to-r ${feature.color} p-4 rounded-2xl group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                        {React.cloneElement(feature.icon, { className: "h-8 w-8 text-white" })}
                      </div>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4 text-center">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600 text-center leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Quotes Section */}
      <section className="py-20 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 relative overflow-hidden">
        {/* Background decorations */}
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full opacity-10 animate-pulse"></div>
          <div className="absolute bottom-10 right-10 w-24 h-24 bg-white rounded-full opacity-10 animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-white rounded-full opacity-10 animate-pulse delay-500"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Words of Wisdom
            </h2>
            <p className="text-xl text-purple-100 max-w-3xl mx-auto">
              Get inspired by the thoughts of visionaries who changed the world
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {quotes.map((quote, index) => (
              <div key={index} className="group">
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-2">
                  <div className="text-center">
                    <div className="text-6xl text-white/30 mb-4">"</div>
                    <p className="text-lg text-white mb-6 leading-relaxed italic">
                      {quote.text}
                    </p>
                    <div className="border-t border-white/20 pt-4">
                      <p className="text-white font-bold text-lg">{quote.author}</p>
                      <p className="text-purple-200 text-sm">{quote.role}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section id="categories" className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-purple-100 text-purple-800 mb-6">
              <Lightbulb className="h-4 w-4 mr-2" />
              Innovation Categories
            </span>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Share Ideas Across All Industries
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              From revolutionary business models to cutting-edge technology solutions,
              we welcome innovations that can transform any industry.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {categories.map((category, index) => (
              <div key={index} className="group relative">
                <div className="bg-white rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                  {/* Category Image */}
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={category.image}
                      alt={category.name}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                    <div className={`absolute inset-0 bg-gradient-to-r ${category.color} opacity-30 group-hover:opacity-40 transition-opacity duration-300`}></div>
                    <div className="absolute inset-0 bg-black opacity-20"></div>
                  </div>

                  <div className="p-6 text-center">
                    <div className={`bg-gradient-to-r ${category.color} w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                      <Rocket className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">{category.name}</h3>
                    <p className="text-gray-600 text-sm mb-4">{category.description}</p>
                    <div className={`w-12 h-1 bg-gradient-to-r ${category.color} rounded-full mx-auto`}></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Idea Database Section */}
      <section id="database" className="py-24 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800 mb-6">
              <CheckCircle className="h-4 w-4 mr-2" />
              Idea Database
            </span>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Learn from Past Innovations
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              Explore our curated collection of innovative ideas from previous competitions.
              Each entry includes detailed analysis by our expert team to help you learn and grow.
            </p>
          </div>

          {/* Database Stats */}
          <div className="grid md:grid-cols-4 gap-6 mb-16">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 text-center border border-blue-100 shadow-lg">
              <div className="bg-gradient-to-r from-blue-500 to-cyan-500 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Lightbulb className="h-6 w-6 text-white" />
              </div>
              <p className="text-3xl font-bold text-gray-900 mb-2">150+</p>
              <p className="text-gray-600">Ideas Analyzed</p>
            </div>
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 text-center border border-green-100 shadow-lg">
              <div className="bg-gradient-to-r from-green-500 to-emerald-500 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Users className="h-6 w-6 text-white" />
              </div>
              <p className="text-3xl font-bold text-gray-900 mb-2">500+</p>
              <p className="text-gray-600">Innovators</p>
            </div>
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 text-center border border-purple-100 shadow-lg">
              <div className="bg-gradient-to-r from-purple-500 to-pink-500 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Trophy className="h-6 w-6 text-white" />
              </div>
              <p className="text-3xl font-bold text-gray-900 mb-2">25</p>
              <p className="text-gray-600">Winners</p>
            </div>
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 text-center border border-orange-100 shadow-lg">
              <div className="bg-gradient-to-r from-orange-500 to-red-500 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Rocket className="h-6 w-6 text-white" />
              </div>
              <p className="text-3xl font-bold text-gray-900 mb-2">12</p>
              <p className="text-gray-600">Launched</p>
            </div>
          </div>

          {/* Featured Ideas */}
          <div className="space-y-8">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold text-gray-900 mb-4">Featured Ideas</h3>
              <p className="text-lg text-gray-600">
                Discover innovative concepts with detailed expert analysis
              </p>
            </div>

            <div className="grid lg:grid-cols-1 gap-8">
              {sampleIdeas.map((idea, index) => (
                <div key={idea.id} className="group">
                  <div className="bg-white rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                    <div className="lg:flex">
                      {/* Image Section */}
                      <div className="lg:w-1/3 relative">
                        <img
                          src={idea.image}
                          alt={idea.title}
                          className="w-full h-64 lg:h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        <div className={`absolute inset-0 bg-gradient-to-r ${idea.color} opacity-20 group-hover:opacity-30 transition-opacity duration-300`}></div>
                      </div>

                      {/* Content Section */}
                      <div className="lg:w-2/3 p-8">
                        <div className="flex items-start justify-between mb-4">
                          <div>
                            <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r ${idea.color} text-white mb-3`}>
                              {idea.category}
                            </span>
                            <h4 className="text-2xl font-bold text-gray-900 mb-2">{idea.title}</h4>
                            <p className="text-gray-600 mb-4">by {idea.submitter}</p>
                          </div>
                        </div>

                        <p className="text-gray-700 mb-6 leading-relaxed">
                          {idea.description}
                        </p>

                        {/* Expert Analysis */}
                        <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 mb-6">
                          <h5 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                            <CheckCircle className="h-5 w-5 text-blue-600 mr-2" />
                            Expert Analysis
                          </h5>
                          <p className="text-gray-700 leading-relaxed">
                            {idea.analysis}
                          </p>
                        </div>

                        {/* Metrics */}
                        <div className="grid grid-cols-3 gap-4 mb-6">
                          <div className="text-center">
                            <p className="text-sm text-gray-600 mb-1">Market Potential</p>
                            <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                              idea.marketPotential === 'Very High' ? 'bg-green-100 text-green-800' :
                              idea.marketPotential === 'High' ? 'bg-blue-100 text-blue-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {idea.marketPotential}
                            </span>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-gray-600 mb-1">Feasibility</p>
                            <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                              idea.feasibility === 'High' ? 'bg-green-100 text-green-800' :
                              idea.feasibility === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {idea.feasibility}
                            </span>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-gray-600 mb-1">Innovation</p>
                            <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                              idea.innovation === 'Very High' ? 'bg-purple-100 text-purple-800' :
                              idea.innovation === 'High' ? 'bg-blue-100 text-blue-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {idea.innovation}
                            </span>
                          </div>
                        </div>

                        {/* Tags */}
                        <div className="flex flex-wrap gap-2">
                          {idea.tags.map((tag, tagIndex) => (
                            <span key={tagIndex} className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">
                              #{tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Call to Action */}
            <div className="text-center mt-16">
              <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-3xl p-8 text-white">
                <h3 className="text-2xl font-bold mb-4">Want to See Your Idea Here?</h3>
                <p className="text-purple-100 mb-6 leading-relaxed">
                  Submit your innovative concept and get expert analysis from our team.
                  The best ideas get featured in our database for the community to learn from.
                </p>
                <Link
                  to="/submit"
                  className="bg-white text-purple-600 hover:bg-gray-100 font-bold py-4 px-8 rounded-2xl inline-flex items-center text-lg shadow-lg transform hover:-translate-y-1 transition-all duration-300"
                >
                  🚀 Submit Your Idea
                  <ArrowRight className="ml-3 h-5 w-5" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-24 bg-gradient-to-br from-cyan-50 via-blue-50 to-indigo-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="relative">
              {/* Innovation Image */}
              <div className="relative rounded-3xl overflow-hidden shadow-2xl">
                <img
                  src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
                  alt="Innovation and Technology"
                  className="w-full h-96 object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-white">
                    <h3 className="text-3xl font-bold mb-2">Innovation Hub</h3>
                    <p className="text-xl opacity-90">Where Ideas Come to Life</p>
                  </div>
                </div>
              </div>

              {/* Floating elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full opacity-20 animate-pulse"></div>
              <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-r from-green-400 to-teal-400 rounded-full opacity-20 animate-pulse delay-1000"></div>
            </div>

            <div>
              <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800 mb-6">
                <Users className="h-4 w-4 mr-2" />
                About Innovation Spark
              </span>
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8 leading-tight">
                Empowering the Next Generation of
                <span className="text-gradient"> Innovators</span>
              </h2>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Innovation Spark is more than just a platform—it's a movement. We believe that
                every great idea deserves a chance to be heard, evaluated, and potentially
                transformed into reality.
              </p>
              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="bg-primary-100 p-2 rounded-lg mr-4 mt-1">
                    <CheckCircle className="h-5 w-5 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Expert Evaluation</h3>
                    <p className="text-gray-600">Our team of industry experts carefully reviews each submission</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-primary-100 p-2 rounded-lg mr-4 mt-1">
                    <Trophy className="h-5 w-5 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Recognition & Rewards</h3>
                    <p className="text-gray-600">Outstanding ideas receive recognition and valuable prizes</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-primary-100 p-2 rounded-lg mr-4 mt-1">
                    <Rocket className="h-5 w-5 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Launch Opportunities</h3>
                    <p className="text-gray-600">Connect with mentors and resources to bring ideas to life</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="relative order-first lg:order-last">
              <div className="bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-3xl p-8 text-white shadow-2xl">
                <div className="text-center mb-6">
                  <div className="bg-white/20 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Rocket className="h-10 w-10 text-white" />
                  </div>
                  <h3 className="text-3xl font-bold mb-4">Ready to Get Started?</h3>
                </div>
                <p className="text-purple-100 mb-8 leading-relaxed text-center">
                  Join thousands of innovators who have already shared their groundbreaking ideas.
                  Your next big breakthrough could be just one submission away.
                </p>
                <div className="text-center">
                  <Link
                    to="/submit"
                    className="bg-white text-purple-600 hover:bg-gray-100 font-bold py-4 px-8 rounded-2xl inline-flex items-center text-lg shadow-lg transform hover:-translate-y-1 transition-all duration-300 hover:shadow-2xl"
                  >
                    🚀 Submit Your Idea
                    <ArrowRight className="ml-3 h-5 w-5" />
                  </Link>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full opacity-30 animate-pulse"></div>
              <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-r from-green-400 to-teal-400 rounded-full opacity-30 animate-pulse delay-1000"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="flex items-center mb-6">
                <div className="relative mr-3">
                  <div className="bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 p-2 rounded-xl">
                    <Zap className="h-6 w-6 text-white" />
                  </div>
                  <div className="absolute -top-1 -right-1 bg-gradient-to-r from-orange-400 to-red-400 p-1 rounded-full">
                    <Timer className="h-3 w-3 text-white" />
                  </div>
                </div>
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                  IdeaSprint
                </span>
              </div>
              <p className="text-gray-400 text-lg leading-relaxed mb-6">
                Accelerating innovation through rapid idea development, expert evaluation,
                and community-driven growth in our sprint-based platform.
              </p>
              <div className="flex space-x-4">
                <div className="bg-gray-800 p-3 rounded-lg">
                  <Lightbulb className="h-5 w-5 text-primary-400" />
                </div>
                <div className="bg-gray-800 p-3 rounded-lg">
                  <Users className="h-5 w-5 text-primary-400" />
                </div>
                <div className="bg-gray-800 p-3 rounded-lg">
                  <Trophy className="h-5 w-5 text-primary-400" />
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-semibold mb-6">Quick Links</h3>
              <ul className="space-y-3">
                <li>
                  <Link to="/submit" className="text-gray-400 hover:text-white transition-colors duration-200 flex items-center">
                    <ArrowRight className="h-4 w-4 mr-2" />
                    Submit Idea
                  </Link>
                </li>
                <li>
                  <Link to="/terms" className="text-gray-400 hover:text-white transition-colors duration-200 flex items-center">
                    <ArrowRight className="h-4 w-4 mr-2" />
                    Terms & Conditions
                  </Link>
                </li>
                <li>
                  <Link to="/privacy" className="text-gray-400 hover:text-white transition-colors duration-200 flex items-center">
                    <ArrowRight className="h-4 w-4 mr-2" />
                    Privacy Policy
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-xl font-semibold mb-6">Get in Touch</h3>
              <p className="text-gray-400 leading-relaxed mb-4">
                Have questions about your submission or our platform?
                We're here to help innovators succeed.
              </p>
              <div className="bg-gray-800 p-4 rounded-lg">
                <p className="text-sm text-gray-300">
                  <strong>Contact us through the platform for support</strong>
                </p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 mb-4 md:mb-0">
                &copy; 2024 IdeaSprint. All rights reserved.
              </p>
              <div className="flex items-center space-x-6">
                <span className="text-gray-500 text-sm">Powered by Innovation</span>
                {/* Hidden admin access - only visible to you */}
                <Link
                  to="/admin/login"
                  className="text-gray-700 hover:text-gray-500 text-xs opacity-30 hover:opacity-100 transition-opacity duration-300"
                  title="Admin Access"
                >
                  •
                </Link>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default LandingPage
