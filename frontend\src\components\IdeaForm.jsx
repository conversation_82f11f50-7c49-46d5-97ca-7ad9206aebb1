import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { ArrowLeft, Send, CheckCircle } from 'lucide-react'
import { ideaAPI } from '../utils/api'
import toast from 'react-hot-toast'

const IdeaForm = () => {
  const navigate = useNavigate()
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm()

  const description = watch('description', '')
  const remainingChars = 2000 - (description?.length || 0)

  const onSubmit = async (data) => {
    setIsSubmitting(true)
    
    try {
      const response = await ideaAPI.submit(data)
      toast.success('Idea submitted successfully!')
      navigate('/success', { 
        state: { 
          ideaId: response.data.idea_id,
          submissionDate: response.data.submission_date 
        }
      })
    } catch (error) {
      const message = error.response?.data?.error || 'Failed to submit idea'
      toast.error(message)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            to="/"
            className="inline-flex items-center text-primary-600 hover:text-primary-700 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Link>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Submit Your Innovation
          </h1>
          <p className="text-lg text-gray-600">
            Share your brilliant idea and compete for the MacBook Air prize!
          </p>
        </div>

        {/* Form */}
        <div className="bg-white shadow-lg rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              Idea Submission Form
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Please fill out all required fields marked with *
            </p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="px-6 py-6 space-y-6">
            {/* Title */}
            <div>
              <label className="form-label">
                Idea Title *
              </label>
              <input
                type="text"
                className={`form-input ${errors.title ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                placeholder="Enter a compelling title for your idea"
                {...register('title', {
                  required: 'Title is required',
                  maxLength: {
                    value: 255,
                    message: 'Title must be less than 255 characters'
                  }
                })}
              />
              {errors.title && (
                <p className="form-error">{errors.title.message}</p>
              )}
            </div>

            {/* Category */}
            <div>
              <label className="form-label">
                Category *
              </label>
              <select
                className={`form-select ${errors.category ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                {...register('category', {
                  required: 'Please select a category'
                })}
              >
                <option value="">Select a category</option>
                <option value="Business">Business</option>
                <option value="Web Application">Web Application</option>
                <option value="Mobile Application">Mobile Application</option>
                <option value="Other">Other</option>
              </select>
              {errors.category && (
                <p className="form-error">{errors.category.message}</p>
              )}
            </div>

            {/* Description */}
            <div>
              <label className="form-label">
                Idea Description *
              </label>
              <textarea
                rows={6}
                className={`form-textarea ${errors.description ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                placeholder="Describe your idea in detail. What problem does it solve? How does it work? What makes it unique?"
                {...register('description', {
                  required: 'Description is required',
                  minLength: {
                    value: 10,
                    message: 'Description must be at least 10 characters'
                  },
                  maxLength: {
                    value: 2000,
                    message: 'Description must be less than 2000 characters'
                  }
                })}
              />
              <div className="flex justify-between items-center mt-1">
                {errors.description && (
                  <p className="form-error">{errors.description.message}</p>
                )}
                <p className={`text-sm ${remainingChars < 100 ? 'text-red-600' : 'text-gray-500'} ml-auto`}>
                  {remainingChars} characters remaining
                </p>
              </div>
            </div>

            {/* Target Audience */}
            <div>
              <label className="form-label">
                Target Audience
              </label>
              <input
                type="text"
                className="form-input"
                placeholder="Who is your target audience? (e.g., young professionals, small businesses)"
                {...register('target_audience', {
                  maxLength: {
                    value: 500,
                    message: 'Target audience must be less than 500 characters'
                  }
                })}
              />
              {errors.target_audience && (
                <p className="form-error">{errors.target_audience.message}</p>
              )}
            </div>

            {/* Monetization Strategy */}
            <div>
              <label className="form-label">
                Monetization Strategy
              </label>
              <textarea
                rows={3}
                className="form-textarea"
                placeholder="How would you make money from this idea? (e.g., subscription model, one-time purchase, advertising)"
                {...register('monetization_strategy', {
                  maxLength: {
                    value: 1000,
                    message: 'Monetization strategy must be less than 1000 characters'
                  }
                })}
              />
              {errors.monetization_strategy && (
                <p className="form-error">{errors.monetization_strategy.message}</p>
              )}
            </div>

            {/* USP */}
            <div>
              <label className="form-label">
                Unique Selling Proposition (USP)
              </label>
              <textarea
                rows={3}
                className="form-textarea"
                placeholder="What makes your idea unique? What's your competitive advantage?"
                {...register('usp', {
                  maxLength: {
                    value: 1000,
                    message: 'USP must be less than 1000 characters'
                  }
                })}
              />
              {errors.usp && (
                <p className="form-error">{errors.usp.message}</p>
              )}
            </div>

            {/* Submitter Information */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Your Information
              </h3>
              
              <div className="grid md:grid-cols-2 gap-6">
                {/* Name */}
                <div>
                  <label className="form-label">
                    Your Name *
                  </label>
                  <input
                    type="text"
                    className={`form-input ${errors.submitter_name ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                    placeholder="Enter your full name"
                    {...register('submitter_name', {
                      required: 'Name is required',
                      maxLength: {
                        value: 255,
                        message: 'Name must be less than 255 characters'
                      }
                    })}
                  />
                  {errors.submitter_name && (
                    <p className="form-error">{errors.submitter_name.message}</p>
                  )}
                </div>

                {/* Email */}
                <div>
                  <label className="form-label">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    className={`form-input ${errors.submitter_email ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                    placeholder="Enter your email address"
                    {...register('submitter_email', {
                      required: 'Email is required',
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: 'Please enter a valid email address'
                      }
                    })}
                  />
                  {errors.submitter_email && (
                    <p className="form-error">{errors.submitter_email.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Terms and Conditions */}
            <div className="border-t border-gray-200 pt-6">
              <div className="flex items-start">
                <input
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded mt-1"
                  {...register('terms_accepted', {
                    required: 'You must accept the terms and conditions'
                  })}
                />
                <div className="ml-3">
                  <label className="text-sm text-gray-700">
                    I agree to the{' '}
                    <Link
                      to="/terms"
                      target="_blank"
                      className="text-primary-600 hover:text-primary-700 underline"
                    >
                      Terms & Conditions
                    </Link>{' '}
                    and{' '}
                    <Link
                      to="/privacy"
                      target="_blank"
                      className="text-primary-600 hover:text-primary-700 underline"
                    >
                      Privacy Policy
                    </Link>
                    *
                  </label>
                  {errors.terms_accepted && (
                    <p className="form-error mt-1">{errors.terms_accepted.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="border-t border-gray-200 pt-6">
              <button
                type="submit"
                disabled={isSubmitting}
                className={`btn-primary w-full text-lg py-4 ${
                  isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Submitting...
                  </>
                ) : (
                  <>
                    <Send className="h-5 w-5 mr-2" />
                    Submit My Idea
                  </>
                )}
              </button>
            </div>
          </form>
        </div>

        {/* Help Text */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Tips for a great submission:</p>
              <ul className="list-disc list-inside space-y-1 text-blue-700">
                <li>Be specific and detailed in your description</li>
                <li>Explain the problem your idea solves</li>
                <li>Highlight what makes your idea unique</li>
                <li>Consider the business potential and target market</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default IdeaForm
