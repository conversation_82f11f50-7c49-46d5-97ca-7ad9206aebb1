import React, { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  Lightbulb, 
  CheckCircle, 
  Clock, 
  TrendingUp,
  Users,
  BarChart3,
  Eye
} from 'lucide-react'
import AdminLayout from './AdminLayout'
import { ideaAPI } from '../../utils/api'
import toast from 'react-hot-toast'

const AdminDashboard = () => {
  const [stats, setStats] = useState(null)
  const [recentIdeas, setRecentIdeas] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      
      // Fetch dashboard stats
      const statsResponse = await ideaAPI.getStats()
      setStats(statsResponse.data)

      // Fetch recent ideas
      const ideasResponse = await ideaAPI.getAll({ 
        limit: 5, 
        sort: 'submission_date', 
        order: 'desc' 
      })
      setRecentIdeas(ideasResponse.data.ideas)

    } catch (error) {
      toast.error('Failed to load dashboard data')
      console.error('Dashboard error:', error)
    } finally {
      setLoading(false)
    }
  }

  const statCards = [
    {
      title: 'Total Ideas',
      value: stats?.total_ideas || 0,
      icon: Lightbulb,
      color: 'bg-blue-500',
      textColor: 'text-blue-600'
    },
    {
      title: 'Reviewed',
      value: stats?.reviewed_ideas || 0,
      icon: CheckCircle,
      color: 'bg-green-500',
      textColor: 'text-green-600'
    },
    {
      title: 'Pending Review',
      value: stats?.pending_review || 0,
      icon: Clock,
      color: 'bg-yellow-500',
      textColor: 'text-yellow-600'
    },
    {
      title: 'Recent (7 days)',
      value: stats?.recent_submissions || 0,
      icon: TrendingUp,
      color: 'bg-purple-500',
      textColor: 'text-purple-600'
    }
  ]

  if (loading) {
    return (
      <AdminLayout title="Dashboard">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading dashboard...</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout title="Dashboard">
      <div className="space-y-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statCards.map((stat, index) => {
            const Icon = stat.icon
            return (
              <div key={index} className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className={`${stat.color} rounded-lg p-3 mr-4`}>
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      {stat.title}
                    </p>
                    <p className={`text-2xl font-bold ${stat.textColor}`}>
                      {stat.value}
                    </p>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Charts and Recent Ideas */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Ideas by Category */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Ideas by Category
              </h3>
            </div>
            <div className="p-6">
              {stats?.ideas_by_category?.length > 0 ? (
                <div className="space-y-4">
                  {stats.ideas_by_category.map((category, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">
                        {category.category}
                      </span>
                      <div className="flex items-center">
                        <div className="w-32 bg-gray-200 rounded-full h-2 mr-3">
                          <div
                            className="bg-primary-600 h-2 rounded-full"
                            style={{
                              width: `${(category.count / stats.total_ideas) * 100}%`
                            }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-600 w-8 text-right">
                          {category.count}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">
                  No ideas submitted yet
                </p>
              )}
            </div>
          </div>

          {/* Recent Ideas */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900 flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  Recent Submissions
                </h3>
                <Link
                  to="/admin/ideas"
                  className="text-sm text-primary-600 hover:text-primary-700"
                >
                  View all
                </Link>
              </div>
            </div>
            <div className="p-6">
              {recentIdeas.length > 0 ? (
                <div className="space-y-4">
                  {recentIdeas.map((idea) => (
                    <div key={idea.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {idea.title}
                        </p>
                        <p className="text-xs text-gray-500">
                          by {idea.submitter_name} • {idea.category}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        {idea.is_reviewed ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <Clock className="h-4 w-4 text-yellow-500" />
                        )}
                        <Link
                          to={`/admin/ideas/${idea.id}`}
                          className="text-primary-600 hover:text-primary-700"
                        >
                          <Eye className="h-4 w-4" />
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">
                  No recent submissions
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              Quick Actions
            </h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link
                to="/admin/ideas"
                className="btn-primary text-center"
              >
                <Lightbulb className="h-4 w-4 mr-2" />
                Review Ideas
              </Link>
              
              <Link
                to="/admin/ideas?filter=pending"
                className="btn-outline text-center"
              >
                <Clock className="h-4 w-4 mr-2" />
                Pending Reviews
              </Link>
              
              <Link
                to="/"
                className="btn-secondary text-center"
              >
                View Public Site
              </Link>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}

export default AdminDashboard
