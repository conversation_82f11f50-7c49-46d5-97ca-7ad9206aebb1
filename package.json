{"name": "innovation-spark", "version": "1.0.0", "description": "Business & Tech Idea Submission Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "npm run build:frontend", "build:frontend": "cd frontend && npm run build", "install:all": "npm install && cd frontend && npm install"}, "keywords": ["ideas", "submission", "platform", "innovation"], "author": "Innovation Spark", "license": "MIT", "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "pg": "^8.11.3"}, "devDependencies": {"nodemon": "^3.0.2"}}