@echo off
echo ========================================
echo Innovation Spark Setup Script
echo ========================================
echo.

REM Check if Docker is running
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not installed or not running.
    echo Please install Docker Desktop and make sure it's running.
    pause
    exit /b 1
)

echo ✓ Docker is available

REM Check if .env file exists
if not exist .env (
    echo Creating .env file from template...
    copy .env.example .env
    echo.
    echo ⚠️  IMPORTANT: Please edit the .env file with your settings before continuing.
    echo    Especially change the passwords and JWT secret!
    echo.
    pause
)

echo ✓ Environment file ready

REM Build and start the application
echo.
echo Building and starting Innovation Spark...
echo This may take a few minutes on first run...
echo.

docker-compose up --build -d

if %errorlevel% neq 0 (
    echo.
    echo ❌ Failed to start the application.
    echo Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ Innovation Spark is now running!
echo ========================================
echo.
echo 🌐 Public Site: http://localhost:3002
echo 🔐 Admin Panel: http://localhost:3002/admin/login
echo 📊 Database Admin: http://localhost:8080 (optional)
echo.
echo Next steps:
echo 1. Open http://localhost:3002/admin/login
echo 2. Click "Need to setup admin account?"
echo 3. Create your admin credentials
echo 4. Start reviewing ideas!
echo.
echo To stop the application: docker-compose down
echo To view logs: docker-compose logs -f app
echo.
pause
