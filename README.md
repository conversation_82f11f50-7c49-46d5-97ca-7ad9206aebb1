# Innovation Spark - Business & Tech Idea Submission Platform

A modern web application for collecting and managing innovative business and technology ideas, with a MacBook Air prize for the best submission.

## 🚀 Features

### Public Features
- **Landing Page**: Attractive homepage with clear call-to-action
- **Idea Submission Form**: Comprehensive form with validation
- **Success Page**: Confirmation and submission details
- **Terms & Privacy**: Legal pages for compliance

### Admin Features
- **Secure Login**: JWT-based authentication
- **Dashboard**: Overview statistics and recent submissions
- **Ideas Management**: View, filter, search, and review ideas
- **Review System**: Mark ideas as reviewed and add admin notes
- **Export Functionality**: Download ideas as CSV
- **Responsive Design**: Works on desktop and mobile

## 🛠 Tech Stack

- **Frontend**: React 18 + Vite + Tailwind CSS
- **Backend**: Node.js + Express
- **Database**: PostgreSQL
- **Authentication**: JWT + bcrypt
- **Containerization**: Docker + Docker Compose
- **Validation**: React Hook Form + express-validator

## 📋 Prerequisites

- **Docker Desktop** (for Windows 11)
- **Git** (for cloning the repository)
- **Web Browser** (Chrome, Firefox, Edge, etc.)

## 🚀 Quick Start with Docker

### 1. Clone the Repository
```bash
git clone <repository-url>
cd innovation-spark
```

### 2. Environment Setup
Copy the example environment file and customize it:
```bash
copy .env.example .env
```

Edit the `.env` file with your settings:
```env
# Database Configuration
DB_HOST=database
DB_PORT=5432
DB_NAME=innovation_spark
DB_USER=postgres
DB_PASSWORD=your_secure_password_here

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production
JWT_EXPIRES_IN=24h

# Server Configuration
PORT=3000
NODE_ENV=production
FRONTEND_URL=http://localhost:3000

# Admin Configuration (for initial setup)
ADMIN_USERNAME=admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=change_this_password_here
```

### 3. Build and Run with Docker
```bash
# Build and start all services
docker-compose up --build

# Or run in background
docker-compose up --build -d
```

### 4. Access the Application
- **Public Site**: http://localhost:3002
- **Admin Panel**: http://localhost:3002/admin/login (hidden access via footer)
- **Database Admin** (optional): http://localhost:8080 (pgAdmin)

### 5. Setup Admin Account
1. Go to http://localhost:3002/admin/login
2. Click "Need to setup admin account?"
3. Create your admin credentials
4. Login with your new credentials

## 🔧 Development Setup

### 1. Install Dependencies
```bash
# Install backend dependencies
npm install

# Install frontend dependencies
cd frontend
npm install
cd ..
```

### 2. Database Setup
Start PostgreSQL with Docker:
```bash
docker-compose up database -d
```

### 3. Environment Variables
Create `.env` file with development settings:
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=innovation_spark
DB_USER=postgres
DB_PASSWORD=your_password
JWT_SECRET=your_jwt_secret
NODE_ENV=development
FRONTEND_URL=http://localhost:5173
```

### 4. Run Development Servers
```bash
# Terminal 1: Backend
npm run dev

# Terminal 2: Frontend
cd frontend
npm run dev
```

## 📊 Database Schema

### Users Table
- `id` - Primary key
- `username` - Unique username
- `email` - Email address
- `password_hash` - Hashed password
- `is_admin` - Admin flag
- `created_at` - Creation timestamp
- `updated_at` - Update timestamp

### Ideas Table
- `id` - Primary key
- `title` - Idea title
- `category` - Business/Web App/Mobile App/Other
- `description` - Detailed description
- `target_audience` - Target market
- `monetization_strategy` - Revenue model
- `usp` - Unique selling proposition
- `submitter_name` - Submitter's name
- `submitter_email` - Submitter's email
- `submission_date` - Submission timestamp
- `is_reviewed` - Review status
- `admin_notes` - Admin comments
- `created_at` - Creation timestamp
- `updated_at` - Update timestamp

## 🔐 Security Features

- **Password Hashing**: bcrypt with salt rounds
- **JWT Authentication**: Secure token-based auth
- **Input Validation**: Server-side and client-side
- **Rate Limiting**: Prevents abuse
- **CORS Protection**: Configured origins
- **Helmet.js**: Security headers
- **SQL Injection Protection**: Parameterized queries

## 📱 API Endpoints

### Public Endpoints
- `POST /api/ideas/submit` - Submit new idea
- `GET /api/health` - Health check

### Admin Endpoints (Authenticated)
- `POST /api/auth/login` - Admin login
- `GET /api/auth/verify` - Verify token
- `POST /api/auth/logout` - Logout
- `GET /api/ideas` - Get all ideas (with pagination/filtering)
- `GET /api/ideas/:id` - Get specific idea
- `PATCH /api/ideas/:id` - Update idea review status
- `GET /api/ideas/stats/dashboard` - Dashboard statistics
- `GET /api/ideas/export/csv` - Export ideas as CSV

### Setup Endpoint
- `POST /api/auth/setup-admin` - Create initial admin user

## 🐳 Docker Commands

```bash
# Build and start all services
docker-compose up --build

# Start services in background
docker-compose up -d

# Stop all services
docker-compose down

# View logs
docker-compose logs app
docker-compose logs database

# Rebuild specific service
docker-compose build app

# Access database directly
docker-compose exec database psql -U postgres -d innovation_spark
```

## 🔍 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Change ports in docker-compose.yml or stop conflicting services
   netstat -ano | findstr :3000
   ```

2. **Database Connection Failed**
   ```bash
   # Check if database is running
   docker-compose ps
   # Restart database
   docker-compose restart database
   ```

3. **Frontend Build Errors**
   ```bash
   # Clear node_modules and reinstall
   cd frontend
   rmdir /s node_modules
   npm install
   ```

### Logs and Debugging
```bash
# View application logs
docker-compose logs -f app

# View database logs
docker-compose logs -f database

# Access container shell
docker-compose exec app sh
```

## 📈 Production Deployment

### Environment Variables for Production
```env
NODE_ENV=production
DB_PASSWORD=strong_production_password
JWT_SECRET=very_long_random_string_for_production
FRONTEND_URL=https://yourdomain.com
```

### Security Checklist
- [ ] Change default passwords
- [ ] Use strong JWT secret
- [ ] Enable HTTPS
- [ ] Configure firewall
- [ ] Set up database backups
- [ ] Monitor application logs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 📞 Support

For questions or issues:
- Create an issue in the repository
- Contact: <EMAIL>

---

**Innovation Spark** - Empowering innovation through idea sharing and recognition.
