import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { CheckCircle, Home, Lightbulb, Trophy } from 'lucide-react'
import { formatDate } from '../utils/api'

const SuccessPage = () => {
  const location = useLocation()
  const { ideaId, submissionDate } = location.state || {}

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <div className="bg-white shadow-lg rounded-lg p-8 text-center">
          {/* Success Icon */}
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
            <CheckCircle className="h-10 w-10 text-green-600" />
          </div>

          {/* Success Message */}
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Idea Submitted Successfully!
          </h1>
          
          <p className="text-gray-600 mb-6">
            Thank you for sharing your innovation with us. We've received your submission and will review it carefully.
          </p>

          {/* Submission Details */}
          {ideaId && (
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <h3 className="text-sm font-medium text-gray-900 mb-2">
                Submission Details
              </h3>
              <div className="text-sm text-gray-600 space-y-1">
                <p>
                  <span className="font-medium">Submission ID:</span> #{ideaId}
                </p>
                {submissionDate && (
                  <p>
                    <span className="font-medium">Submitted:</span> {formatDate(submissionDate)}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* What's Next */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 className="text-sm font-medium text-blue-900 mb-2 flex items-center">
              <Lightbulb className="h-4 w-4 mr-2" />
              What happens next?
            </h3>
            <ul className="text-sm text-blue-800 space-y-1 text-left">
              <li>• Our team will review your submission</li>
              <li>• We'll evaluate all ideas based on innovation and potential</li>
              <li>• The winner will be contacted via email</li>
              <li>• Results will be announced soon!</li>
            </ul>
          </div>

          {/* Prize Reminder */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-center mb-2">
              <Trophy className="h-5 w-5 text-yellow-600 mr-2" />
              <span className="text-sm font-medium text-yellow-900">
                Grand Prize: MacBook Air
              </span>
            </div>
            <p className="text-xs text-yellow-800">
              The most innovative idea wins!
            </p>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Link
              to="/"
              className="btn-primary w-full inline-flex items-center justify-center"
            >
              <Home className="h-4 w-4 mr-2" />
              Back to Home
            </Link>
            
            <Link
              to="/submit"
              className="btn-outline w-full inline-flex items-center justify-center"
            >
              <Lightbulb className="h-4 w-4 mr-2" />
              Submit Another Idea
            </Link>
          </div>

          {/* Contact Info */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              Questions about your submission?{' '}
              <a href="mailto:<EMAIL>" className="text-primary-600 hover:text-primary-700">
                Contact us
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SuccessPage
