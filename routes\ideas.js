const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { pool } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Submit new idea (public endpoint)
router.post('/submit', [
  body('title').trim().isLength({ min: 1, max: 255 }).withMessage('Title is required and must be less than 255 characters'),
  body('category').isIn(['Business', 'Web Application', 'Mobile Application', 'Other']).withMessage('Invalid category'),
  body('description').trim().isLength({ min: 10, max: 2000 }).withMessage('Description must be between 10 and 2000 characters'),
  body('target_audience').optional().trim().isLength({ max: 500 }).withMessage('Target audience must be less than 500 characters'),
  body('monetization_strategy').optional().trim().isLength({ max: 1000 }).withMessage('Monetization strategy must be less than 1000 characters'),
  body('usp').optional().trim().isLength({ max: 1000 }).withMessage('USP must be less than 1000 characters'),
  body('submitter_name').trim().isLength({ min: 1, max: 255 }).withMessage('Submitter name is required'),
  body('submitter_email').isEmail().withMessage('Valid email is required'),
  body('terms_accepted').equals('true').withMessage('Terms and conditions must be accepted')
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const {
      title,
      category,
      description,
      target_audience,
      monetization_strategy,
      usp,
      submitter_name,
      submitter_email
    } = req.body;

    // Generate a random submission ID for display (not the database ID)
    const generateRandomId = () => {
      const prefix = 'IS'; // IdeaSprint prefix
      const randomNum = Math.floor(Math.random() * 900000) + 100000; // 6-digit number
      return `${prefix}${randomNum}`;
    };

    // Insert new idea
    const result = await pool.query(
      `INSERT INTO ideas (
        title, category, description, target_audience,
        monetization_strategy, usp, submitter_name, submitter_email
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING id, submission_date`,
      [title, category, description, target_audience, monetization_strategy, usp, submitter_name, submitter_email]
    );

    const newIdea = result.rows[0];
    const displayId = generateRandomId();

    res.status(201).json({
      message: 'Idea submitted successfully! Thank you for your submission.',
      idea_id: displayId, // Use random ID instead of database ID
      submission_date: newIdea.submission_date
    });

  } catch (error) {
    console.error('Idea submission error:', error);
    res.status(500).json({ error: 'Failed to submit idea. Please try again.' });
  }
});

// Get all ideas (admin only)
router.get('/', authenticateToken, [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('category').optional().isIn(['Business', 'Web Application', 'Mobile Application', 'Other']).withMessage('Invalid category'),
  query('sort').optional().isIn(['submission_date', 'title', 'category']).withMessage('Invalid sort field'),
  query('order').optional().isIn(['asc', 'desc']).withMessage('Invalid sort order'),
  query('search').optional().trim().isLength({ max: 255 }).withMessage('Search term too long')
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    const category = req.query.category;
    const sort = req.query.sort || 'submission_date';
    const order = req.query.order || 'desc';
    const search = req.query.search;

    // Build query
    let whereClause = '';
    let queryParams = [];
    let paramCount = 0;

    if (category) {
      paramCount++;
      whereClause += `WHERE category = $${paramCount}`;
      queryParams.push(category);
    }

    if (search) {
      paramCount++;
      const searchClause = `(title ILIKE $${paramCount} OR description ILIKE $${paramCount} OR submitter_name ILIKE $${paramCount})`;
      whereClause += whereClause ? ` AND ${searchClause}` : `WHERE ${searchClause}`;
      queryParams.push(`%${search}%`);
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM ideas ${whereClause}`;
    const countResult = await pool.query(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].total);

    // Get ideas
    queryParams.push(limit, offset);
    const ideasQuery = `
      SELECT
        id, title, category, description, target_audience,
        monetization_strategy, usp, submitter_name, submitter_email,
        submission_date, is_reviewed, admin_notes
      FROM ideas
      ${whereClause}
      ORDER BY ${sort} ${order.toUpperCase()}
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;

    const ideasResult = await pool.query(ideasQuery, queryParams);

    res.json({
      ideas: ideasResult.rows,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get ideas error:', error);
    res.status(500).json({ error: 'Failed to retrieve ideas' });
  }
});

// Get single idea by ID (admin only)
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const ideaId = parseInt(req.params.id);

    if (isNaN(ideaId)) {
      return res.status(400).json({ error: 'Invalid idea ID' });
    }

    const result = await pool.query(
      `SELECT
        id, title, category, description, target_audience,
        monetization_strategy, usp, submitter_name, submitter_email,
        submission_date, is_reviewed, admin_notes, created_at, updated_at
      FROM ideas WHERE id = $1`,
      [ideaId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Idea not found' });
    }

    res.json({ idea: result.rows[0] });

  } catch (error) {
    console.error('Get idea error:', error);
    res.status(500).json({ error: 'Failed to retrieve idea' });
  }
});

// Update idea review status and admin notes (admin only)
router.patch('/:id', authenticateToken, [
  body('is_reviewed').optional().isBoolean().withMessage('is_reviewed must be a boolean'),
  body('admin_notes').optional().trim().isLength({ max: 2000 }).withMessage('Admin notes must be less than 2000 characters')
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const ideaId = parseInt(req.params.id);
    const { is_reviewed, admin_notes } = req.body;

    if (isNaN(ideaId)) {
      return res.status(400).json({ error: 'Invalid idea ID' });
    }

    // Build update query dynamically
    let updateFields = [];
    let queryParams = [];
    let paramCount = 0;

    if (typeof is_reviewed === 'boolean') {
      paramCount++;
      updateFields.push(`is_reviewed = $${paramCount}`);
      queryParams.push(is_reviewed);
    }

    if (admin_notes !== undefined) {
      paramCount++;
      updateFields.push(`admin_notes = $${paramCount}`);
      queryParams.push(admin_notes);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({ error: 'No valid fields to update' });
    }

    // Add updated_at timestamp
    paramCount++;
    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);

    // Add WHERE clause parameter
    paramCount++;
    queryParams.push(ideaId);

    const updateQuery = `
      UPDATE ideas
      SET ${updateFields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING id, is_reviewed, admin_notes, updated_at
    `;

    const result = await pool.query(updateQuery, queryParams);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Idea not found' });
    }

    res.json({
      message: 'Idea updated successfully',
      idea: result.rows[0]
    });

  } catch (error) {
    console.error('Update idea error:', error);
    res.status(500).json({ error: 'Failed to update idea' });
  }
});

// Get dashboard statistics (admin only)
router.get('/stats/dashboard', authenticateToken, async (req, res) => {
  try {
    // Get total ideas count
    const totalResult = await pool.query('SELECT COUNT(*) as total FROM ideas');
    const total = parseInt(totalResult.rows[0].total);

    // Get reviewed ideas count
    const reviewedResult = await pool.query('SELECT COUNT(*) as reviewed FROM ideas WHERE is_reviewed = TRUE');
    const reviewed = parseInt(reviewedResult.rows[0].reviewed);

    // Get ideas by category
    const categoryResult = await pool.query(`
      SELECT category, COUNT(*) as count
      FROM ideas
      GROUP BY category
      ORDER BY count DESC
    `);

    // Get recent submissions (last 7 days)
    const recentResult = await pool.query(`
      SELECT COUNT(*) as recent
      FROM ideas
      WHERE submission_date >= NOW() - INTERVAL '7 days'
    `);
    const recent = parseInt(recentResult.rows[0].recent);

    res.json({
      total_ideas: total,
      reviewed_ideas: reviewed,
      pending_review: total - reviewed,
      recent_submissions: recent,
      ideas_by_category: categoryResult.rows
    });

  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({ error: 'Failed to retrieve dashboard statistics' });
  }
});

// Export ideas to CSV (admin only)
router.get('/export/csv', authenticateToken, async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT
        id, title, category, description, target_audience,
        monetization_strategy, usp, submitter_name, submitter_email,
        submission_date, is_reviewed, admin_notes
      FROM ideas
      ORDER BY submission_date DESC
    `);

    // Create CSV content
    const headers = [
      'ID', 'Title', 'Category', 'Description', 'Target Audience',
      'Monetization Strategy', 'USP', 'Submitter Name', 'Submitter Email',
      'Submission Date', 'Is Reviewed', 'Admin Notes'
    ];

    let csvContent = headers.join(',') + '\n';

    result.rows.forEach(row => {
      const csvRow = [
        row.id,
        `"${(row.title || '').replace(/"/g, '""')}"`,
        `"${(row.category || '').replace(/"/g, '""')}"`,
        `"${(row.description || '').replace(/"/g, '""')}"`,
        `"${(row.target_audience || '').replace(/"/g, '""')}"`,
        `"${(row.monetization_strategy || '').replace(/"/g, '""')}"`,
        `"${(row.usp || '').replace(/"/g, '""')}"`,
        `"${(row.submitter_name || '').replace(/"/g, '""')}"`,
        `"${(row.submitter_email || '').replace(/"/g, '""')}"`,
        row.submission_date ? row.submission_date.toISOString() : '',
        row.is_reviewed ? 'Yes' : 'No',
        `"${(row.admin_notes || '').replace(/"/g, '""')}"`
      ];
      csvContent += csvRow.join(',') + '\n';
    });

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="innovation-spark-ideas-${new Date().toISOString().split('T')[0]}.csv"`);
    res.send(csvContent);

  } catch (error) {
    console.error('Export CSV error:', error);
    res.status(500).json({ error: 'Failed to export ideas' });
  }
});

module.exports = router;
