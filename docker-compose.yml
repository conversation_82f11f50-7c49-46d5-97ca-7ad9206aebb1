version: '3.8'

services:
  # PostgreSQL Database
  database:
    image: postgres:15-alpine
    container_name: innovation-spark-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: innovation_spark
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD:-secure_password_change_me}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - innovation-spark-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d innovation_spark"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Main Application
  app:
    build: .
    container_name: innovation-spark-app
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DB_HOST: database
      DB_PORT: 5432
      DB_NAME: innovation_spark
      DB_USER: postgres
      DB_PASSWORD: ${DB_PASSWORD:-secure_password_change_me}
      JWT_SECRET: ${JWT_SECRET:-your_super_secret_jwt_key_change_this_in_production}
      JWT_EXPIRES_IN: 24h
      PORT: 3000
      FRONTEND_URL: http://localhost:3002
    ports:
      - "3002:3000"
    depends_on:
      database:
        condition: service_healthy
    networks:
      - innovation-spark-network
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: pgAdmin for database management (development)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: innovation-spark-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin123}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    depends_on:
      - database
    networks:
      - innovation-spark-network
    profiles:
      - development

volumes:
  postgres_data:
    driver: local

networks:
  innovation-spark-network:
    driver: bridge
