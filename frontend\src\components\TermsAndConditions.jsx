import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { ArrowLeft, FileText } from 'lucide-react'

const TermsAndConditions = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            to="/"
            className="inline-flex items-center text-primary-600 hover:text-primary-700 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Link>
          <div className="flex items-center mb-4">
            <FileText className="h-8 w-8 text-primary-600 mr-3" />
            <h1 className="text-3xl font-bold text-gray-900">
              Terms & Conditions
            </h1>
          </div>
          <p className="text-gray-600">
            Last updated: {new Date().toLocaleDateString()}
          </p>
        </div>

        {/* Content */}
        <div className="bg-white shadow-lg rounded-lg p-8">
          <div className="prose max-w-none">
            <h2>1. Acceptance of Terms</h2>
            <p>
              By submitting an idea to Innovation Spark, you agree to be bound by these Terms and Conditions. 
              If you do not agree to these terms, please do not submit your idea.
            </p>

            <h2>2. Idea Submission</h2>
            <p>
              By submitting your idea, you represent and warrant that:
            </p>
            <ul>
              <li>The idea is your original work</li>
              <li>You have the right to submit the idea</li>
              <li>The idea does not infringe on any third-party rights</li>
              <li>All information provided is accurate and truthful</li>
            </ul>

            <h2>3. Intellectual Property</h2>
            <p>
              You retain ownership of your submitted idea. However, by submitting your idea, you grant 
              Innovation Spark a non-exclusive, royalty-free license to review, evaluate, and display 
              your idea for the purposes of this competition.
            </p>

            <h2>4. Competition Rules</h2>
            <p>
              The competition is open to individuals worldwide. Ideas will be evaluated based on:
            </p>
            <ul>
              <li>Innovation and creativity</li>
              <li>Market potential</li>
              <li>Feasibility</li>
              <li>Uniqueness</li>
            </ul>

            <h2>5. Prize</h2>
            <p>
              The grand prize is a MacBook Air. The prize will be awarded to the submitter of the most 
              innovative idea as determined by our evaluation panel. The decision is final and binding.
            </p>

            <h2>6. Privacy</h2>
            <p>
              Your personal information will be handled in accordance with our Privacy Policy. 
              We will not share your idea with third parties without your consent.
            </p>

            <h2>7. Limitation of Liability</h2>
            <p>
              Innovation Spark shall not be liable for any damages arising from your participation 
              in this competition or the use of our platform.
            </p>

            <h2>8. Modification of Terms</h2>
            <p>
              We reserve the right to modify these terms at any time. Changes will be posted on this page 
              with an updated date.
            </p>

            <h2>9. Contact Information</h2>
            <p>
              If you have any questions about these Terms and Conditions, please contact us at:
              <br />
              Email: <EMAIL>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TermsAndConditions
