# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=innovation_spark
DB_USER=postgres
DB_PASSWORD=your_secure_password

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production
JWT_EXPIRES_IN=24h

# Server Configuration
PORT=3000
NODE_ENV=development
FRONTEND_URL=http://localhost:5173

# Admin Configuration (for initial setup)
ADMIN_USERNAME=admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=change_this_password

# Application Settings
MAX_IDEA_LENGTH=2000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
