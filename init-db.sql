-- Innovation Spark Database Initialization Script
-- This script will be run when the PostgreSQL container starts for the first time

-- Create the database if it doesn't exist (handled by POSTGRES_DB env var)
-- CREATE DATABASE IF NOT EXISTS innovation_spark;

-- Connect to the database
\c innovation_spark;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    is_admin BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create ideas table
CREATE TABLE IF NOT EXISTS ideas (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL CHECK (category IN ('Business', 'Web Application', 'Mobile Application', 'Other')),
    description TEXT NOT NULL,
    target_audience TEXT,
    monetization_strategy TEXT,
    usp TEXT,
    submitter_name VA<PERSON>HAR(255) NOT NULL,
    submitter_email VARCHAR(255) NOT NULL,
    submission_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_reviewed BOOLEAN DEFAULT FALSE,
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_ideas_category ON ideas(category);
CREATE INDEX IF NOT EXISTS idx_ideas_submission_date ON ideas(submission_date DESC);
CREATE INDEX IF NOT EXISTS idx_ideas_is_reviewed ON ideas(is_reviewed);
CREATE INDEX IF NOT EXISTS idx_ideas_submitter_email ON ideas(submitter_email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ideas_updated_at BEFORE UPDATE ON ideas
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert admin user with specific credentials
-- Password: milind123godse123@123@123@123
-- This is hashed using bcrypt with 12 salt rounds
INSERT INTO users (username, email, password_hash, is_admin) VALUES (
    'milindgodse',
    '<EMAIL>',
    '$2b$12$T8sDR2VJv4nmEU6bBnDyS.Hqylx.Ova37efAXdP3EyOhtgls1i4Ya',
    TRUE
) ON CONFLICT (username) DO NOTHING;

-- Insert some sample data for testing (optional)
-- Uncomment the following lines if you want sample data

-- INSERT INTO ideas (
--     title, category, description, target_audience,
--     monetization_strategy, usp, submitter_name, submitter_email
-- ) VALUES
-- (
--     'AI-Powered Personal Finance Assistant',
--     'Mobile Application',
--     'A mobile app that uses artificial intelligence to help users manage their personal finances, track expenses, and provide personalized investment advice.',
--     'Young professionals aged 25-35 who want to improve their financial literacy',
--     'Freemium model with premium features for advanced analytics and investment recommendations',
--     'First AI assistant that learns from user behavior and provides truly personalized financial advice',
--     'John Doe',
--     '<EMAIL>'
-- ),
-- (
--     'Sustainable Food Delivery Network',
--     'Business',
--     'A food delivery service that focuses exclusively on sustainable, locally-sourced restaurants and uses electric vehicles for delivery.',
--     'Environmentally conscious consumers in urban areas',
--     'Commission-based model with premium delivery fees for eco-friendly service',
--     'Only delivery service with 100% sustainable supply chain and carbon-neutral delivery',
--     'Jane Smith',
--     '<EMAIL>'
-- );

-- Grant necessary permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;
