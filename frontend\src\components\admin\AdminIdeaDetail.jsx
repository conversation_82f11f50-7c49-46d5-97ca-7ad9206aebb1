import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom'
import { 
  ArrowLeft, 
  CheckCircle, 
  Clock, 
  Save,
  User,
  Mail,
  Calendar,
  Tag,
  Target,
  DollarSign,
  Star,
  FileText
} from 'lucide-react'
import AdminLayout from './AdminLayout'
import { ideaAPI, formatDate } from '../../utils/api'
import toast from 'react-hot-toast'

const AdminIdeaDetail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [idea, setIdea] = useState(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [isReviewed, setIsReviewed] = useState(false)
  const [adminNotes, setAdminNotes] = useState('')

  useEffect(() => {
    fetchIdea()
  }, [id])

  const fetchIdea = async () => {
    try {
      setLoading(true)
      const response = await ideaAPI.getById(id)
      const ideaData = response.data.idea
      setIdea(ideaData)
      setIsReviewed(ideaData.is_reviewed)
      setAdminNotes(ideaData.admin_notes || '')
    } catch (error) {
      toast.error('Failed to load idea details')
      console.error('Fetch idea error:', error)
      navigate('/admin/ideas')
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      await ideaAPI.update(id, {
        is_reviewed: isReviewed,
        admin_notes: adminNotes
      })
      toast.success('Idea updated successfully!')
      
      // Update local state
      setIdea(prev => ({
        ...prev,
        is_reviewed: isReviewed,
        admin_notes: adminNotes
      }))
    } catch (error) {
      toast.error('Failed to update idea')
      console.error('Update idea error:', error)
    } finally {
      setSaving(false)
    }
  }

  const getCategoryColor = (category) => {
    const colors = {
      'Business': 'bg-blue-100 text-blue-800',
      'Web Application': 'bg-green-100 text-green-800',
      'Mobile Application': 'bg-purple-100 text-purple-800',
      'Other': 'bg-gray-100 text-gray-800'
    }
    return colors[category] || colors['Other']
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading idea details...</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  if (!idea) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Idea not found</h3>
          <Link to="/admin/ideas" className="btn-primary">
            Back to Ideas
          </Link>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Link
              to="/admin/ideas"
              className="btn-outline mr-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Ideas
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Idea Details</h1>
              <p className="text-gray-600">ID: #{idea.id}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {idea.is_reviewed ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <Clock className="h-5 w-5 text-yellow-500" />
            )}
            <span className={`px-3 py-1 rounded-full text-sm ${
              idea.is_reviewed 
                ? 'bg-green-100 text-green-800' 
                : 'bg-yellow-100 text-yellow-800'
            }`}>
              {idea.is_reviewed ? 'Reviewed' : 'Pending Review'}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Idea Information */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-900">
                  {idea.title}
                </h2>
                <div className="flex items-center mt-2">
                  <span className={`px-3 py-1 rounded-full text-sm ${getCategoryColor(idea.category)}`}>
                    {idea.category}
                  </span>
                </div>
              </div>
              
              <div className="p-6 space-y-6">
                {/* Description */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    Description
                  </h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p className="text-gray-700 whitespace-pre-wrap">
                      {idea.description}
                    </p>
                  </div>
                </div>

                {/* Target Audience */}
                {idea.target_audience && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                      <Target className="h-5 w-5 mr-2" />
                      Target Audience
                    </h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <p className="text-gray-700">{idea.target_audience}</p>
                    </div>
                  </div>
                )}

                {/* Monetization Strategy */}
                {idea.monetization_strategy && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                      <DollarSign className="h-5 w-5 mr-2" />
                      Monetization Strategy
                    </h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <p className="text-gray-700 whitespace-pre-wrap">
                        {idea.monetization_strategy}
                      </p>
                    </div>
                  </div>
                )}

                {/* USP */}
                {idea.usp && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                      <Star className="h-5 w-5 mr-2" />
                      Unique Selling Proposition
                    </h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <p className="text-gray-700 whitespace-pre-wrap">
                        {idea.usp}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Submitter Information */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                  Submitter Information
                </h3>
              </div>
              <div className="p-6 space-y-4">
                <div className="flex items-center">
                  <User className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm text-gray-600">Name</p>
                    <p className="font-medium text-gray-900">{idea.submitter_name}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Mail className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm text-gray-600">Email</p>
                    <a 
                      href={`mailto:${idea.submitter_email}`}
                      className="font-medium text-primary-600 hover:text-primary-700"
                    >
                      {idea.submitter_email}
                    </a>
                  </div>
                </div>
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm text-gray-600">Submitted</p>
                    <p className="font-medium text-gray-900">
                      {formatDate(idea.submission_date)}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Review Status */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                  Review Status
                </h3>
              </div>
              <div className="p-6 space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="reviewed"
                    checked={isReviewed}
                    onChange={(e) => setIsReviewed(e.target.checked)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label htmlFor="reviewed" className="ml-3 text-sm font-medium text-gray-700">
                    Mark as reviewed
                  </label>
                </div>
                
                <div>
                  <label className="form-label">
                    Admin Notes
                  </label>
                  <textarea
                    rows={4}
                    className="form-textarea"
                    placeholder="Add your notes about this idea..."
                    value={adminNotes}
                    onChange={(e) => setAdminNotes(e.target.value)}
                  />
                </div>

                <button
                  onClick={handleSave}
                  disabled={saving}
                  className={`btn-primary w-full ${
                    saving ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  {saving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Changes
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* Existing Admin Notes */}
            {idea.admin_notes && (
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">
                    Previous Notes
                  </h3>
                </div>
                <div className="p-6">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p className="text-gray-700 whitespace-pre-wrap">
                      {idea.admin_notes}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}

export default AdminIdeaDetail
