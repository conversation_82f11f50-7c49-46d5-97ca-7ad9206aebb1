import React, { useState, useEffect } from 'react'
import { Link, useSearchParams } from 'react-router-dom'
import { 
  Search, 
  Filter, 
  Eye, 
  CheckCircle, 
  Clock,
  ChevronLeft,
  ChevronRight,
  Calendar,
  User,
  Tag
} from 'lucide-react'
import AdminLayout from './AdminLayout'
import { ideaAPI, formatDate, truncateText } from '../../utils/api'
import toast from 'react-hot-toast'

const AdminIdeaList = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  const [ideas, setIdeas] = useState([])
  const [pagination, setPagination] = useState({})
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState({
    search: searchParams.get('search') || '',
    category: searchParams.get('category') || '',
    sort: searchParams.get('sort') || 'submission_date',
    order: searchParams.get('order') || 'desc',
    page: parseInt(searchParams.get('page')) || 1
  })

  useEffect(() => {
    fetchIdeas()
  }, [filters])

  const fetchIdeas = async () => {
    try {
      setLoading(true)
      const response = await ideaAPI.getAll(filters)
      setIdeas(response.data.ideas)
      setPagination(response.data.pagination)
    } catch (error) {
      toast.error('Failed to load ideas')
      console.error('Fetch ideas error:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateFilters = (newFilters) => {
    const updatedFilters = { ...filters, ...newFilters, page: 1 }
    setFilters(updatedFilters)
    
    // Update URL params
    const params = new URLSearchParams()
    Object.entries(updatedFilters).forEach(([key, value]) => {
      if (value) params.set(key, value)
    })
    setSearchParams(params)
  }

  const handlePageChange = (newPage) => {
    const updatedFilters = { ...filters, page: newPage }
    setFilters(updatedFilters)
    
    const params = new URLSearchParams(searchParams)
    params.set('page', newPage)
    setSearchParams(params)
  }

  const getStatusIcon = (isReviewed) => {
    return isReviewed ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <Clock className="h-4 w-4 text-yellow-500" />
    )
  }

  const getStatusText = (isReviewed) => {
    return isReviewed ? 'Reviewed' : 'Pending'
  }

  const getCategoryColor = (category) => {
    const colors = {
      'Business': 'bg-blue-100 text-blue-800',
      'Web Application': 'bg-green-100 text-green-800',
      'Mobile Application': 'bg-purple-100 text-purple-800',
      'Other': 'bg-gray-100 text-gray-800'
    }
    return colors[category] || colors['Other']
  }

  return (
    <AdminLayout title="Ideas Management">
      <div className="space-y-6">
        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="md:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search ideas, titles, or submitters..."
                  className="form-input pl-10"
                  value={filters.search}
                  onChange={(e) => updateFilters({ search: e.target.value })}
                />
              </div>
            </div>

            {/* Category Filter */}
            <div>
              <select
                className="form-select"
                value={filters.category}
                onChange={(e) => updateFilters({ category: e.target.value })}
              >
                <option value="">All Categories</option>
                <option value="Business">Business</option>
                <option value="Web Application">Web Application</option>
                <option value="Mobile Application">Mobile Application</option>
                <option value="Other">Other</option>
              </select>
            </div>

            {/* Sort */}
            <div>
              <select
                className="form-select"
                value={`${filters.sort}-${filters.order}`}
                onChange={(e) => {
                  const [sort, order] = e.target.value.split('-')
                  updateFilters({ sort, order })
                }}
              >
                <option value="submission_date-desc">Newest First</option>
                <option value="submission_date-asc">Oldest First</option>
                <option value="title-asc">Title A-Z</option>
                <option value="title-desc">Title Z-A</option>
                <option value="category-asc">Category A-Z</option>
              </select>
            </div>
          </div>
        </div>

        {/* Ideas List */}
        <div className="bg-white rounded-lg shadow">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Loading ideas...</p>
              </div>
            </div>
          ) : ideas.length === 0 ? (
            <div className="text-center py-12">
              <Filter className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No ideas found</h3>
              <p className="text-gray-600">
                {filters.search || filters.category 
                  ? 'Try adjusting your filters to see more results.'
                  : 'No ideas have been submitted yet.'
                }
              </p>
            </div>
          ) : (
            <>
              {/* Table Header */}
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">
                    Ideas ({pagination.total || 0})
                  </h3>
                  <div className="text-sm text-gray-600">
                    Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                    {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                    {pagination.total} results
                  </div>
                </div>
              </div>

              {/* Ideas List */}
              <div className="divide-y divide-gray-200">
                {ideas.map((idea) => (
                  <div key={idea.id} className="p-6 hover:bg-gray-50">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        {/* Title and Status */}
                        <div className="flex items-center mb-2">
                          <h4 className="text-lg font-medium text-gray-900 truncate mr-3">
                            {idea.title}
                          </h4>
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(idea.is_reviewed)}
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              idea.is_reviewed 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {getStatusText(idea.is_reviewed)}
                            </span>
                          </div>
                        </div>

                        {/* Description */}
                        <p className="text-gray-600 mb-3">
                          {truncateText(idea.description, 150)}
                        </p>

                        {/* Meta Information */}
                        <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                          <div className="flex items-center">
                            <Tag className="h-4 w-4 mr-1" />
                            <span className={`px-2 py-1 rounded-full text-xs ${getCategoryColor(idea.category)}`}>
                              {idea.category}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-1" />
                            <span>{idea.submitter_name}</span>
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            <span>{formatDate(idea.submission_date)}</span>
                          </div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="ml-4 flex-shrink-0">
                        <Link
                          to={`/admin/ideas/${idea.id}`}
                          className="btn-outline text-sm"
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              {pagination.pages > 1 && (
                <div className="px-6 py-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-600">
                      Page {pagination.page} of {pagination.pages}
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handlePageChange(pagination.page - 1)}
                        disabled={pagination.page <= 1}
                        className="btn-outline text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <ChevronLeft className="h-4 w-4 mr-1" />
                        Previous
                      </button>
                      <button
                        onClick={() => handlePageChange(pagination.page + 1)}
                        disabled={pagination.page >= pagination.pages}
                        className="btn-outline text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Next
                        <ChevronRight className="h-4 w-4 ml-1" />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </AdminLayout>
  )
}

export default AdminIdeaList
